<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delwest Bungalow - Explore Sri Lanka</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #1a3a2e 0%, #16423c 50%, #0f2027 100%);
            min-height: 100vh;
            color: #2c3e50;
            line-height: 1.6;
        }

        .features-section {
            padding: 80px 20px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="leaves" patternUnits="userSpaceOnUse" width="20" height="20"><path d="M10 5c5 0 5 10 0 10s-5-10 0-10z" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23leaves)"/></svg>');
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-header {
            text-align: center;
            margin-bottom: 70px;
            color: white;
        }

        .section-pretitle {
            font-size: 1rem;
            color: #7fb069;
            text-transform: uppercase;
            letter-spacing: 3px;
            margin-bottom: 15px;
            font-weight: 400;
        }

        .section-title {
            font-size: 3.5rem;
            color: #ffffff;
            margin-bottom: 25px;
            font-weight: 300;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #7fb069, #a8d8a8);
        }

        .section-subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.8);
            max-width: 700px;
            margin: 0 auto;
            font-weight: 300;
            line-height: 1.8;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            border: 1px solid rgba(127, 176, 105, 0.2);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #7fb069, #a8d8a8, #c8e6c9);
        }

        .feature-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 35px 70px rgba(127, 176, 105, 0.2);
        }

        .feature-card > img {
            width: 100%;
            height: 220px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .feature-card:hover > img {
            transform: scale(1.05);
        }

        .card-content {
            padding: 40px 35px 30px;
        }



        .card-title {
            font-size: 1.9rem;
            color: #1a3a2e;
            margin-bottom: 15px;
            font-weight: 400;
            letter-spacing: -0.5px;
        }

        .card-description {
            font-size: 1.1rem;
            color: #5a6c57;
            margin-bottom: 30px;
            line-height: 1.7;
        }

        .images-gallery {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-bottom: 30px;
            border-radius: 15px;
            overflow: hidden;
        }

        .gallery-image {
            width: 100%;
            height: 140px;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
        }

        .gallery-image:hover {
            transform: scale(1.1);
            z-index: 2;
            border-radius: 10px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }

        .gallery-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(127, 176, 105, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .gallery-image:hover::after {
            opacity: 1;
        }



        /* Nature-inspired decorative elements */
        .leaf-decoration {
            position: absolute;
            width: 30px;
            height: 30px;
            opacity: 0.1;
            background: #7fb069;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }

        .leaf-decoration.leaf-1 {
            top: 20px;
            right: 20px;
            transform: rotate(45deg);
        }

        .leaf-decoration.leaf-2 {
            bottom: 20px;
            left: 20px;
            transform: rotate(-45deg);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(26, 58, 46, 0.95);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        .modal-image {
            margin: auto;
            display: block;
            max-width: 85%;
            max-height: 85%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 10px;
        }
        .close {
            position: absolute;
            top: 25px;
            right: 45px;
            color: white;
            font-size: 50px;
            font-weight: 300;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .close:hover {
            color: #bbb;
            transform: scale(1.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .section-title {
                font-size: 2.5rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .feature-card {
                margin: 0 10px;
            }
            
            .card-content {
                padding: 30px 25px 25px;
            }
            
            .gallery-image {
                height: 120px;
            }
        }

        @media (max-width: 480px) {
            .section-title {
                font-size: 2rem;
            }
            
            .card-title {
                font-size: 1.5rem;
            }
            
            .features-section {
                padding: 60px 15px;
            }
        }
    </style>
</head>
<body>
    <section class="features-section">
        <div class="container">
            <div class="section-header">
                <p class="section-pretitle">Discover Nature's Wonders</p>
                <h2 class="section-title">Explore Sri Lanka</h2>
                <p class="section-subtitle">From the comfort of Delwest Bungalow, embark on unforgettable journeys to Sri Lanka's most breathtaking natural destinations, where pristine wilderness meets timeless beauty.</p>
            </div>

            <div class="features-grid">
                <!-- Hakgala Gardens -->
                <div class="feature-card">
                    <img src="https://delwest.lk/storage/explore/hakgala-gardens/imagesssss.jpg" alt="Hakgala Gardens">
                    <div class="leaf-decoration leaf-1"></div>
                    <div class="leaf-decoration leaf-2"></div>
                    <div class="card-content">
                        <h3 class="card-title">Hakgala Gardens</h3>
                        <p class="card-description">Immerse yourself in a botanical paradise where rare endemic species flourish in the cool mountain climate. This high-altitude garden showcases the incredible biodiversity of Sri Lanka's hill country.</p>

                        <div class="images-gallery">
                            <img src="https://delwest.lk/storage/explore/hakgala-gardens/greatgardens-hakgala-botanic-garden-06.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                            <img src="https://delwest.lk/storage/explore/hakgala-gardens/imssage.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                            <img src="https://delwest.lk/storage/explore/hakgala-gardens/hakgala-botanical-gardens-title-photo-orig.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                        </div>
                    </div>
                </div>

                <!-- Seetha Amman Temple -->
                <div class="feature-card">
                    <img src="https://delwest.lk/storage/explore/seetha-amman-temple/seetha-amman-temple-1200x600.jpg" alt="Seetha Amman Temple">
                    <div class="leaf-decoration leaf-1"></div>
                    <div class="leaf-decoration leaf-2"></div>
                    <div class="card-content">
                        <h3 class="card-title">Seetha Amman Temple</h3>
                        <p class="card-description">Visit this sacred Hindu temple believed to be where Sita was held captive in the Ramayana epic. The temple features beautiful architecture and offers spiritual tranquility amidst lush surroundings.</p>

                        <div class="images-gallery">
                            <img src="https://delwest.lk/storage/explore/seetha-amman-temple/seetha-amman-temple-2.jpg" alt="Seetha Amman Temple" class="gallery-image" onclick="openModal(this)">
                            <img src="https://delwest.lk/storage/explore/seetha-amman-temple/images.jpg" alt="Temple Architecture" class="gallery-image" onclick="openModal(this)">
                            <img src="https://delwest.lk/storage/explore/seetha-amman-temple/seetha-amman-temple.jpg" alt="Temple Gardens" class="gallery-image" onclick="openModal(this)">
                        </div>
                    </div>
                </div>

                <!-- Bomuru Ella Waterfall -->
                <div class="feature-card">
                    <img src="https://delwest.lk/storage/explore/bomuru-ella-waterfall/bomuru-ella.jpg" alt="Bomuru Ella Waterfall">
                    <div class="leaf-decoration leaf-1"></div>
                    <div class="leaf-decoration leaf-2"></div>
                    <div class="card-content">
                        <h3 class="card-title">Bomuru Ella Waterfall</h3>
                        <p class="card-description">Bomuru Ella Waterfall lies tucked away in a lush forest, perfect for those looking to explore off the beaten path. The waterfall cascades gracefully from a height of over 50 meters, creating a soothing, misty breeze and a calming soundtrack of flowing water.</p>

                        <div class="images-gallery">
                            <img src="https://delwest.lk/storage/explore/bomuru-ella-waterfall/ella-004.jpg" alt="Bomuru Ella Waterfall" class="gallery-image" onclick="openModal(this)">
                            <img src="https://delwest.lk/storage/explore/bomuru-ella-waterfall/bomuru-ella2.jpg" alt="Bomuru Ella Waterfall" class="gallery-image" onclick="openModal(this)">
                            <img src="https://delwest.lk/storage/explore/bomuru-ella-waterfall/43545321285-35cf9abaaf-b.jpg" alt="Bomuru Ella Waterfall" class="gallery-image" onclick="openModal(this)">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-image" id="modalImage">
    </div>

    <script>
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('modalImage');

        function openModal(img) {
            modal.style.display = 'block';
            modalImg.src = img.src;
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        modal.onclick = function(event) {
            if (event.target === modal) {
                closeModal();
            }
        }
        
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        // Smooth scroll and intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe feature cards for scroll animations
        document.querySelectorAll('.feature-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = `opacity 0.6s ease ${index * 0.2}s, transform 0.6s ease ${index * 0.2}s`;
            observer.observe(card);
        });



        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
    <div style="text-align: center; padding: 50px;">
        <a href="more-explore.html" class="explore-btn" style="background: linear-gradient(135deg, #1a3a2e 0%, #16423c 50%, #0f2027 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 50px; font-weight: bold; font-size: 1.2em; transition: transform 0.3s, box-shadow 0.3s;">Explore More</a>
    </div>
</body>
</html>