<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore More Near Nuwara Eliya</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: #0f2027; /* fallback for old browsers */
            background: -webkit-linear-gradient(to right, #38761d, #203A43, #38761d); /* Chrome 10-25, Safari 5.1-6 */
            background: linear-gradient(to right, #38761d, #203A43, #38761d); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
            color: #f0f0f0;
        }
        .header {
            text-align: center;
            padding: 60px 20px;
            background: rgba(0,0,0,0.3);
        }
        .header h1 {
            font-size: 3.5em;
            margin: 0;
            font-weight: 300;
            letter-spacing: 2px;
        }
        .header p {
            font-size: 1.2em;
            color: #a7c4bc;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            padding: 40px;
            max-width: 1400px;
            margin: 0 auto;
        }

        @media (min-width: 768px) {
            .container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1200px) {
            .container {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        .card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 16px 40px 0 rgba(0, 0, 0, 0.5);
        }
        .card img {
            width: 100%;
            height: 220px;
            object-fit: cover;
        }
        .card-content {
            padding: 25px;
        }
        .card-content h3 {
            font-size: 1.8em;
            margin-top: 0;
            color: #7fb069;
        }
        .card-content p {
            font-size: 1em;
            line-height: 1.6;
            color: #c5c5c5;
        }
        .images-gallery {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-top: 25px;
            border-radius: 10px;
            overflow: hidden;
        }
        .gallery-image {
            width: 100%;
            height: 100px;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
        }
        .gallery-image:hover {
            transform: scale(1.08);
            z-index: 2;
            border-radius: 5px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(26, 58, 46, 0.95);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        .modal-image {
            margin: auto;
            display: block;
            max-width: 85%;
            max-height: 85%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 10px;
        }
        .close {
            position: absolute;
            top: 25px;
            right: 45px;
            color: white;
            font-size: 50px;
            font-weight: 300;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .close:hover {
            color: #bbb;
            transform: scale(1.1);
        }

        .footer {
            text-align: center;
            padding: 20px;
            margin-top: 40px;
            background: rgba(0,0,0,0.2);
            font-size: 0.9em;
        }
    </style>
</head>
<body>

    <div class="header">
        <h1>More to Explore</h1>
        <p>Discover the breathtaking landscapes and hidden gems around Nuwara Eliya.</p>
    </div>

    
    <div class="container">

                <!-- Card 9: Hakgala Gardens -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/hakgala-gardens/imagesssss.jpg" alt="Hakgala Gardens">
            <div class="card-content">
                <h3>Hakgala Gardens</h3>
                <p>Immerse yourself in a botanical paradise where rare endemic species flourish in the cool mountain climate. This high-altitude garden showcases the incredible biodiversity of Sri Lanka's hill country.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/hakgala-gardens/greatgardens-hakgala-botanic-garden-06.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/hakgala-gardens/imssage.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/hakgala-gardens/hakgala-botanical-gardens-title-photo-orig.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

                <!-- Card 11: Seetha Amman Temple -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/seetha-amman-temple/seetha-amman-temple-1200x600.jpg" alt="Seetha Amman Temple">
            <div class="card-content">
                <h3>Seetha Amman Temple</h3>
                <p>Visit this sacred Hindu temple believed to be where Sita was held captive in the Ramayana epic. The temple features beautiful architecture and offers spiritual tranquility amidst lush surroundings.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/seetha-amman-temple/seetha-amman-temple-2.jpg" alt="Seetha Amman Temple" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/seetha-amman-temple/images.jpg" alt="Temple Architecture" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/seetha-amman-temple/seetha-amman-temple.jpg" alt="Temple Gardens" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>
        <!-- Card 5: Bomuru Ella Waterfall -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/bomuru-ella-waterfall/bomuru-ella.jpg" alt="Aberdeen Waterfalls">
            <div class="card-content">
                <h3>Bomuru Ella Waterfall</h3>
                <p>Bomuru Ella Waterfall lies tucked away in a lush forest, perfect for those looking to explore off
the beaten path. The waterfall cascades gracefully from a height of over 50 meters, creating a
soothing, misty breeze and a calming soundtrack of flowing water.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/bomuru-ella-waterfall/ella-004.jpg" alt="Aberdeen Waterfalls" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/bomuru-ella-waterfall/bomuru-ella2.jpg" alt="Aberdeen Waterfalls" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/bomuru-ella-waterfall/43545321285-35cf9abaaf-b.jpg" alt="Aberdeen Waterfalls" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 12: Ambewela Farm -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/ambewela-farm/nuwara-eliya-ambewela-farm.jpg" alt="Ambewela Farm">
            <div class="card-content">
                <h3>Ambewela Farm</h3>
                <p>Discover New Zealand-style dairy farming in the heart of Sri Lanka's hill country. Watch grazing cattle against rolling green hills, visit the dairy processing facility, and enjoy fresh dairy products.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/ambewela-farm/ambewela-farm-booking.jpg" alt="Ambewela Farm" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/ambewela-farm/ambewela-new-zeland-farm.jpg" alt="Dairy Cattle" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/ambewela-farm/ambewela-farm-cattle.jpg" alt="Rolling Hills" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 8: Pattipola Station -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/pattipola-station/pattipola-railway-station.jpg" alt="Pattipola Railway Station">
            <div class="card-content">
                <h3>Pattipola Station</h3>
                <p>Journey to Sri Lanka's highest railway station, perched at 1,898 meters above sea level. Surrounded by emerald tea plantations and rolling hills, this remote station offers a glimpse into authentic highland life.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/pattipola-station/pattipola-railway-station.jpg" alt="Pattipola Railway Station" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/pattipola-station/504506697-3127330374113900-1634256031504468854-n.jpg" alt="Pattipola Station Platform" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/pattipola-station/pattipola-station-is-the-highest-14848.jpg" alt="Pattipola Station Sign" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 7: World's End -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/worlds-end/what-a-signboard-2017.jpg" alt="World's End">
            <div class="card-content">
                <h3>World's End</h3>
                <p>Stand at the edge of eternity where dramatic cliffs plunge into misty valleys. This iconic precipice in Horton Plains offers breathtaking panoramic views that stretch to the southern coast on clear days.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/worlds-end/photo0jpg.jpg" alt="World's End" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/worlds-end/imagess-1.jpg" alt="World's End" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/worlds-end/sl-horton-plains-np-asv2020-01-img15.jpg" alt="World's End" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        
    </div>

    <div class="footer">
        <p>Happy Exploring!</p>
    </div>

    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-image" id="modalImage">
    </div>

    <script>
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('modalImage');

        function openModal(img) {
            modal.style.display = 'block';
            modalImg.src = img.src;
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        modal.onclick = function(event) {
            if (event.target === modal) {
                closeModal();
            }
        }
        
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>